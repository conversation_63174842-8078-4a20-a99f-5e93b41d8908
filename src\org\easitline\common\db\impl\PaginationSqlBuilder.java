package org.easitline.common.db.impl;

import org.easitline.common.db.DBTypes;

/**
 * 数据库分页SQL生成工具类
 */
public class PaginationSqlBuilder {
    
    /**
     * 根据数据库类型生成对应的分页SQL
     *
     * @param sql 原始SQL语句
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param dbType 数据库类型
     * @param pageSql 自定义分页SQL,如果不为空则直接返回
     * @param isSubQuery 是否子查询
     * @return 分页SQL
     */
    public static String setPagination(String sql, int pageNum, int pageSize, DBTypes dbType, String pageSql, boolean isSubQuery) {
        // 不分页的情况
        if (pageNum == -1 && pageSize == -1) {
            return sql;
        }
        
        // 参数校验
        if (pageNum <= 0){
            pageNum = 1;
        }
        if (pageSize <= 0){
            pageSize = 50;
        }
        
        // 自定义分页SQL
        if(pageSql != null){
            return pageSql;
        }

        // 根据数据库类型生成分页SQL
        if (dbType == DBTypes.MYSQL || dbType == DBTypes.SQLITE) {
            return toMysqlPageSql(sql, pageNum, pageSize, isSubQuery);
        } else if (dbType == DBTypes.PostgreSql || dbType == DBTypes.OPENGAUSS || dbType == DBTypes.OSCAR) {
            return toPostgresqlPageSql(sql, pageNum, pageSize, isSubQuery);
        } else if (dbType == DBTypes.DB2) {
            return toDb2PageSql(sql, pageNum, pageSize);
        } else if (dbType == DBTypes.ORACLE || dbType == DBTypes.DERBY || dbType == DBTypes.DAMENG) {
            return toOraclePageSql(sql, pageNum, pageSize);
        } else if(dbType == DBTypes.SYBASE){
            return toSybasePageSql(sql, pageNum, pageSize);
        } else if(dbType == DBTypes.SQLSERVER) {
            return toSqlserverPageSql(sql, pageNum, pageSize);
        } else {
            return toMysqlPageSql(sql, pageNum, pageSize, isSubQuery);
        }
    }

    /**
     * MySQL/SQLite分页SQL生成
     * 使用 LIMIT 语法
     */
    private static String toMysqlPageSql(String sql, int pageNo, int pageSize, boolean isSubQuery) {
        if(isSubQuery) {
            return "SELECT * FROM (" + sql + ") A_A LIMIT " + (pageNo - 1) * pageSize + "," + pageSize;
        } else {
            return sql + " LIMIT " + (pageNo - 1) * pageSize + "," + pageSize;
        }
    }

    /**
     * PostgreSQL分页SQL生成
     * 使用 LIMIT OFFSET 语法
     */
    private static String toPostgresqlPageSql(String sql, int pageNo, int pageSize, boolean isSubQuery) {
        if(isSubQuery) {
            return "SELECT * FROM (" + sql + ") A_A LIMIT " + pageSize + " offset " + (pageNo - 1) * pageSize;
        } else {
            return sql + " LIMIT " + pageSize + " offset " + (pageNo - 1) * pageSize;
        }
    }

    /**
     * Sybase分页SQL生成
     * TODO: 待实现
     */
    private static String toSybasePageSql(String sql, int pageNo, int pageSize) {
        return sql;
    }

    /**
     * DB2分页SQL生成
     * TODO: 待实现具体分页逻辑
     */
    private static String toDb2PageSql(String sql, int pageNo, int pageSize) {
        return sql;
    }

    /**
     * Oracle分页SQL生成
     * 使用 ROWNUM 语法
     */
    private static String toOraclePageSql(String sql, int pageNo, int pageSize) {
        int start = (pageNo - 1) * pageSize;
        int end = start + pageSize;
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM (SELECT ROWNUM R_R,A_A.* FROM (")
          .append(sql)
          .append(") A_A WHERE ROWNUM<=")
          .append(end)
          .append(") B_B WHERE R_R>")
          .append(start);
        return sb.toString();
    }

    /**
     * SQL Server分页SQL生成
     * 使用 ROW_NUMBER() OVER 语法
     * 
     * 分页SQL结构:
     * SELECT * FROM 
     *   (SELECT ROW_NUMBER()OVER(ORDER BY _COL)_ROW_NUM,* FROM 
     *     (SELECT TOP start+pageSize _COL=0,* FROM SQL) _TA 
     *   ) _TB 
     * WHERE _ROW_NUM>start
     */
    private static String toSqlserverPageSql(String sql, int pageNo, int pageSize) {
        int start = (pageNo - 1) * pageSize;
        sql = sql.trim();
        if(sql.toLowerCase().startsWith("select")){
            sql = "select TOP 100 PERCENT " + sql.substring(6,sql.length());
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM (SELECT ROW_NUMBER()OVER(ORDER BY _COL)_ROW_NUM,* FROM (SELECT TOP ")
          .append(start + pageSize)
          .append(" _COL=0,* FROM (")
          .append(sql)
          .append(") _TC) _TA) _TB WHERE _ROW_NUM>")
          .append(start);
        return sb.toString();
    }

    /**
     * 移除SQL中的ORDER BY子句，用于COUNT查询性能优化
     * 只移除最外层的ORDER BY，不影响子查询中的ORDER BY
     * 支持各种空白字符组合、换行、注释等复杂情况
     *
     * @param sql 原始SQL语句
     * @return 移除ORDER BY后的SQL语句
     */
    public static String removeOrderByForCount(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        sql = sql.trim();

        // 查找最后一个ORDER BY的位置
        int lastOrderByIndex = -1;
        int parenthesesLevel = 0;
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;

        // 从前往后扫描，记录括号层级、引号状态和注释状态
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            char nextChar = (i + 1 < sql.length()) ? sql.charAt(i + 1) : '\0';

            // 处理行注释 --
            if (!inSingleQuote && !inDoubleQuote && !inBlockComment && c == '-' && nextChar == '-') {
                inLineComment = true;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理块注释开始 /*
            if (!inSingleQuote && !inDoubleQuote && !inLineComment && c == '/' && nextChar == '*') {
                inBlockComment = true;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理块注释结束 */
            if (inBlockComment && c == '*' && nextChar == '/') {
                inBlockComment = false;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理行注释结束（换行）
            if (inLineComment && (c == '\n' || c == '\r')) {
                inLineComment = false;
            }

            // 如果在注释内，跳过
            if (inLineComment || inBlockComment) {
                continue;
            }

            // 处理引号状态
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }

            // 如果在引号内，跳过
            if (inSingleQuote || inDoubleQuote) {
                continue;
            }

            // 处理括号层级
            if (c == '(') {
                parenthesesLevel++;
            } else if (c == ')') {
                parenthesesLevel--;
            }

            // 只在最外层（parenthesesLevel == 0）查找ORDER BY
            if (parenthesesLevel == 0) {
                int orderByPos = findOrderByAt(sql, i);
                if (orderByPos != -1) {
                    // 确保ORDER BY前面是空白字符或者是SQL开始
                    if (orderByPos == 0 || Character.isWhitespace(sql.charAt(orderByPos - 1))) {
                        lastOrderByIndex = orderByPos;
                        // 继续查找，找到最后一个ORDER BY
                    }
                }
            }
        }

        // 如果找到了最外层的ORDER BY，则移除它
        if (lastOrderByIndex != -1) {
            return sql.substring(0, lastOrderByIndex).trim();
        }

        return sql;
    }

    /**
     * 在指定位置查找ORDER BY关键字，支持ORDER和BY之间的各种空白字符
     *
     * @param sql SQL字符串
     * @param startPos 开始查找的位置
     * @return 如果找到ORDER BY则返回ORDER的起始位置，否则返回-1
     */
    private static int findOrderByAt(String sql, int startPos) {
        if (startPos >= sql.length()) {
            return -1;
        }

        String remaining = sql.substring(startPos).toLowerCase();

        // 检查是否以"order"开头
        if (!remaining.startsWith("order")) {
            return -1;
        }

        // 检查ORDER后面是否跟着空白字符和BY
        int pos = 5; // "order"的长度

        // 跳过ORDER后面的空白字符
        while (pos < remaining.length() && Character.isWhitespace(remaining.charAt(pos))) {
            pos++;
        }

        // 检查是否是"by"
        if (pos + 2 <= remaining.length() && remaining.substring(pos, pos + 2).equals("by")) {
            // 确保BY后面是空白字符或字符串结束
            if (pos + 2 == remaining.length() || Character.isWhitespace(remaining.charAt(pos + 2))) {
                return startPos;
            }
        }

        return -1;
    }
}
