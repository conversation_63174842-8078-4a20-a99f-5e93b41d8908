# 超时控制日志记录指南

## 日志级别优化

为了避免将正常的超时保护机制误认为系统错误，我们对日志级别进行了优化：

### 日志级别说明

| 场景 | 日志级别 | 说明 |
|------|----------|------|
| **客户端超时** | `WARN` | 正常的超时保护机制，不是错误 |
| **线程池满载** | `WARN` | 高负载情况，需要关注但不是错误 |
| **stmt.cancel()异常** | `WARN` | 取消操作失败，通常不影响业务 |
| **其他SQL异常** | `ERROR` | 真正的系统错误 |

### 优化前后对比

#### 优化前（问题）
```
2025-08-07 14:00:05.770 [http-nio-9060-exec-14] ERROR easitline-jdbc-error - 客户端超时，已调用stmt.cancel()取消数据库查询
java.util.concurrent.TimeoutException: null
```
❌ **问题**: 正常的超时保护被记录为ERROR，容易误导运维人员

#### 优化后（正确）
```
2025-08-07 14:00:05.770 [http-nio-9060-exec-14] WARN easitline-jdbc-error - 客户端超时，已调用stmt.cancel()取消数据库查询，超时时间: 30秒
```
✅ **改进**: 
- 使用WARN级别，表明这是预期的保护行为
- 包含超时时间信息，便于分析
- 不记录堆栈跟踪，减少日志噪音

## 日志示例

### 1. 正常超时保护
```
WARN easitline-jdbc-error - 客户端超时，已调用stmt.cancel()取消数据库查询，超时时间: 30秒
```

### 2. 线程池满载降级
```
WARN easitline-jdbc-error - 数据库超时控制线程池满载，降级到当前线程执行
WARN easitline-jdbc-error - 线程池状态: DB超时控制线程池 - 核心线程:8, 最大线程:16, 活跃线程:16, 队列大小:500, 完成任务:1024
```

### 3. 取消操作异常
```
WARN easitline-jdbc-error - 调用stmt.cancel()时发生异常: Connection is closed
```

### 4. 真正的系统错误
```
ERROR easitline-jdbc-error - 执行SQL时发生异常
java.sql.SQLException: Database connection failed
```

## 监控建议

### 1. 告警配置

```yaml
# 日志监控配置示例
alerts:
  # ERROR级别立即告警
  - level: ERROR
    pattern: "easitline-jdbc-error"
    action: immediate_alert
    
  # WARN级别统计告警（频率过高时告警）
  - level: WARN
    pattern: "客户端超时"
    threshold: 100/hour
    action: frequency_alert
    
  - level: WARN
    pattern: "线程池满载"
    threshold: 10/hour
    action: capacity_alert
```

### 2. 日志分析

#### 超时频率分析
```bash
# 统计超时频率
grep "客户端超时" app.log | wc -l

# 按小时统计超时次数
grep "客户端超时" app.log | awk '{print $1" "$2}' | cut -d: -f1 | sort | uniq -c
```

#### 线程池状态分析
```bash
# 查看线程池满载情况
grep "线程池满载" app.log

# 分析线程池状态变化
grep "线程池状态" app.log | tail -10
```

### 3. 性能调优指标

根据日志信息进行性能调优：

| 日志模式 | 可能原因 | 调优建议 |
|----------|----------|----------|
| **频繁超时** | SQL执行时间过长 | 优化SQL、增加超时时间 |
| **线程池满载** | 并发量过高 | 增加线程池大小、优化业务逻辑 |
| **取消操作失败** | 数据库连接问题 | 检查连接池配置 |

## 最佳实践

### 1. 日志级别使用原则

- **ERROR**: 真正的系统错误，需要立即处理
- **WARN**: 需要关注但不影响业务的情况
- **INFO**: 重要的业务流程信息
- **DEBUG**: 详细的调试信息

### 2. 超时时间设置建议

```java
// 根据业务场景设置合理的超时时间
query.setTimeout(30);  // 普通查询：30秒
query.setTimeout(60);  // 复杂查询：60秒
query.setTimeout(120); // 报表查询：120秒
```

### 3. 监控仪表板

建议在监控系统中创建以下指标：

- 超时次数/小时
- 线程池使用率
- 平均SQL执行时间
- 超时SQL的分布情况

## 故障排查

### 1. 超时问题排查

```bash
# 1. 查看超时频率
grep "客户端超时" app.log | tail -20

# 2. 分析超时时间分布
grep "超时时间:" app.log | awk -F'超时时间: ' '{print $2}' | awk '{print $1}' | sort | uniq -c

# 3. 查看线程池状态
grep "线程池状态" app.log | tail -5
```

### 2. 性能问题排查

```bash
# 1. 查看线程池满载情况
grep "线程池满载" app.log | wc -l

# 2. 分析高峰时段
grep "线程池满载" app.log | awk '{print $1" "$2}' | cut -d: -f1 | sort | uniq -c

# 3. 查看降级执行情况
grep "降级到当前线程执行" app.log | wc -l
```

## 总结

通过优化日志级别，我们实现了：

- ✅ **准确的错误识别**: ERROR级别只记录真正的系统错误
- ✅ **合理的告警策略**: WARN级别用于需要关注但不紧急的情况
- ✅ **便于运维监控**: 清晰的日志信息便于自动化监控
- ✅ **有效的故障排查**: 结构化的日志便于问题定位

这种日志策略既保证了系统的可观测性，又避免了误报和日志噪音。
