# 超时控制JVM环境变量使用指南

## 概述

`enableTimeoutControl` 现在支持通过JVM环境变量进行配置，参考了 `useSqlHint` 的实现模式，提供了更灵活的配置方式。

## 配置方式

### 1. JVM启动参数配置

```bash
# 启用超时控制（默认行为）
java -Denable.timeout.control=true -jar your-app.jar

# 禁用超时控制
java -Denable.timeout.control=false -jar your-app.jar

# 不设置参数，默认启用
java -jar your-app.jar
```

### 2. 配置优先级

1. **运行时动态设置** > **JVM环境变量** > **默认值(true)**

```java
// 优先级示例
System.setProperty("enable.timeout.control", "false");  // JVM环境变量
ConnectionHelper.setTimeoutControlEnabled(true);        // 运行时设置（优先级更高）
boolean enabled = ConnectionHelper.isTimeoutControlEnabled(); // 返回 true
```

### 3. 缓存机制

配置读取后会被缓存，避免重复读取JVM属性：

```java
// 第一次调用时读取JVM环境变量并缓存
boolean first = ConnectionHelper.isTimeoutControlEnabled();

// 后续调用使用缓存值，不再读取JVM属性
boolean second = ConnectionHelper.isTimeoutControlEnabled();

// 重置缓存，重新读取JVM环境变量
ConnectionHelper.resetTimeoutControlCache();
boolean third = ConnectionHelper.isTimeoutControlEnabled();
```

## 使用场景

### 场景1：开发环境禁用超时控制

```bash
# 开发环境启动，禁用超时控制以便调试
java -Denable.timeout.control=false -jar app.jar
```

### 场景2：生产环境启用超时控制

```bash
# 生产环境启动，启用超时控制（默认）
java -jar app.jar
# 或明确指定
java -Denable.timeout.control=true -jar app.jar
```

### 场景3：运行时动态切换

```java
// 应用启动时从JVM环境变量读取配置
boolean initialState = ConnectionHelper.isTimeoutControlEnabled();

// 运行时根据业务需要动态调整
if (isHighTrafficPeriod()) {
    ConnectionHelper.setTimeoutControlEnabled(false); // 高峰期禁用以提升性能
} else {
    ConnectionHelper.setTimeoutControlEnabled(true);  // 平时启用以保护系统
}
```

### 场景4：配置热更新

```java
// 配置更新后重置缓存
public void updateTimeoutControlConfig(boolean newValue) {
    // 更新JVM属性
    System.setProperty("enable.timeout.control", String.valueOf(newValue));
    
    // 重置缓存，使新配置生效
    ConnectionHelper.resetTimeoutControlCache();
    
    // 验证配置已生效
    boolean currentState = ConnectionHelper.isTimeoutControlEnabled();
    logger.info("超时控制配置已更新为: " + currentState);
}
```

## 配置验证

### 检查当前配置状态

```java
// 检查是否启用
boolean enabled = ConnectionHelper.isTimeoutControlEnabled();
System.out.println("超时控制状态: " + (enabled ? "启用" : "禁用"));

// 检查线程池状态
String status = ConnectionHelper.getTimeoutExecutorStatus();
System.out.println("线程池状态: " + status);
```

### 配置测试

```java
@Test
public void testTimeoutControlConfiguration() {
    // 设置JVM属性
    System.setProperty("enable.timeout.control", "false");
    ConnectionHelper.resetTimeoutControlCache();
    
    // 验证配置生效
    assertFalse("应该禁用超时控制", ConnectionHelper.isTimeoutControlEnabled());
    
    // 运行时覆盖
    ConnectionHelper.setTimeoutControlEnabled(true);
    assertTrue("运行时设置应该覆盖JVM属性", ConnectionHelper.isTimeoutControlEnabled());
}
```

## 最佳实践

### 1. 环境隔离

```bash
# 开发环境
-Denable.timeout.control=false

# 测试环境  
-Denable.timeout.control=true

# 生产环境
-Denable.timeout.control=true
```

### 2. 监控和告警

```java
// 定期检查配置状态
@Scheduled(fixedRate = 60000) // 每分钟检查一次
public void monitorTimeoutControlStatus() {
    boolean enabled = ConnectionHelper.isTimeoutControlEnabled();
    String status = ConnectionHelper.getTimeoutExecutorStatus();
    
    // 记录状态日志
    logger.info("超时控制状态: {}, 线程池状态: {}", enabled, status);
    
    // 异常情况告警
    if (!enabled && isProductionEnvironment()) {
        alertService.sendAlert("生产环境超时控制已禁用，请检查配置");
    }
}
```

### 3. 配置文档化

在应用配置文档中明确说明：

```yaml
# application.yml 注释示例
# JVM参数配置:
# -Denable.timeout.control=true   # 启用数据库超时控制（推荐）
# -Denable.timeout.control=false  # 禁用数据库超时控制（仅调试时使用）
```

## 与useSqlHint的对比

| 特性 | useSqlHint | enableTimeoutControl |
|------|------------|---------------------|
| **JVM属性名** | `use.sql.hint` | `enable.timeout.control` |
| **默认值** | `true` | `true` |
| **缓存机制** | ✅ | ✅ |
| **运行时覆盖** | ❌ | ✅ |
| **重置缓存** | ✅ | ✅ |
| **大小写不敏感** | ✅ | ✅ |

## 总结

通过JVM环境变量支持，`enableTimeoutControl` 现在提供了更灵活的配置方式：

- ✅ **启动时配置**: 通过JVM参数设置初始状态
- ✅ **运行时调整**: 支持动态修改配置
- ✅ **缓存优化**: 避免重复读取JVM属性
- ✅ **配置验证**: 提供状态查询接口
- ✅ **环境隔离**: 不同环境使用不同配置

这种设计既保持了与现有代码的兼容性，又提供了更强的配置灵活性。
