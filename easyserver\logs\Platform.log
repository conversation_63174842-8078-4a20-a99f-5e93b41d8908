2025-08-06 16:19:56	INFO	[undefine:Platform]	containerType->tomcat	 at org.easitline.common.core.context.ServerContext.setContainerType(ServerContext.java:194)	
2025-08-06 16:19:56	INFO	[undefine:Platform]	Engine->重新加载Mars系统配置!	 at org.easitline.common.core.context.ServerContext.reload(ServerContext.java:291)	
2025-08-06 16:19:56	INFO	[undefine:Platform]	驱动包sqlite-jdbc.jar不存在>>>org.sqlite.JDBC	 at org.easitline.common.core.context.ServerContext.getSqliteQuery(ServerContext.java:89)	
2025-08-06 16:19:56	INFO	[undefine:Platform]	EasyEngine->始加载Mars全局配置失败，原因：无法创建PreparedStatement，连接失败	 at org.easitline.common.core.context.ServerContext.reload(ServerContext.java:303)	
2025-08-06 16:19:56	INFO	[undefine:Platform]	驱动包sqlite-jdbc.jar不存在>>>org.sqlite.JDBC	 at org.easitline.common.core.context.ServerContext.getSqliteQuery(ServerContext.java:89)	
2025-08-06 16:19:56	ERROR	[undefine:Platform]	EasyEngine->初始化系统数据源失败,原因：+无法创建PreparedStatement，连接失败
java.sql.SQLException: 无法创建PreparedStatement，连接失败
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:686)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:55)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: java.sql.SQLException: No suitable driver found for **************************************
	at java.sql.DriverManager.getConnection(DriverManager.java:689)
	at java.sql.DriverManager.getConnection(DriverManager.java:247)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:69)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:675)
	... 26 more
2025-08-06 16:19:56	ERROR	[undefine:Platform]	初始化数据源失败!
java.sql.SQLException: EasyEngine->初始化系统数据源失败,原因：+无法创建PreparedStatement，连接失败
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:58)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
