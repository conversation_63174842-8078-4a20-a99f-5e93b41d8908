package org.easitline.common.db.helper;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池状态信息封装类
 * 提供线程池的详细状态信息，便于监控和管理
 */
public class ExecutorStatus {
	
	private final boolean initialized;
	private final boolean shutdown;
	private final boolean enabled;
	private final int corePoolSize;
	private final int maximumPoolSize;
	private final int activeCount;
	private final int queueSize;
	private final long completedTaskCount;
	private final String poolName;
	private final String statusMessage;
	
	/** 未初始化状态 */
	public static ExecutorStatus uninitialized(String poolName) {
		return new ExecutorStatus(false, false, false, 0, 0, 0, 0, 0, poolName, poolName + "未初始化");
	}
	
	/** 已关闭状态 */
	public static ExecutorStatus shutdown(String poolName) {
		return new ExecutorStatus(false, true, false, 0, 0, 0, 0, 0, poolName, poolName + "已关闭");
	}
	
	/** 禁用状态 */
	public static ExecutorStatus disabled(String poolName) {
		return new ExecutorStatus(false, false, false, 0, 0, 0, 0, 0, poolName, poolName + "已禁用");
	}
	
	/** 从ThreadPoolExecutor创建状态 */
	public static ExecutorStatus fromThreadPoolExecutor(ThreadPoolExecutor executor, String poolName) {
		if (executor == null) {
			return uninitialized(poolName);
		}
		
		if (executor.isShutdown()) {
			return shutdown(poolName);
		}
		
		int corePoolSize = executor.getCorePoolSize();
		int maximumPoolSize = executor.getMaximumPoolSize();
		int activeCount = executor.getActiveCount();
		int queueSize = executor.getQueue().size();
		long completedTaskCount = executor.getCompletedTaskCount();
		
		String statusMessage = String.format("%s - 核心线程:%d, 最大线程:%d, 活跃线程:%d, 队列大小:%d, 完成任务:%d",
			poolName, corePoolSize, maximumPoolSize, activeCount, queueSize, completedTaskCount);
		
		return new ExecutorStatus(true, false, true, corePoolSize, maximumPoolSize, 
			activeCount, queueSize, completedTaskCount, poolName, statusMessage);
	}
	
	private ExecutorStatus(boolean initialized, boolean shutdown, boolean enabled,
			int corePoolSize, int maximumPoolSize, int activeCount, 
			int queueSize, long completedTaskCount, String poolName, String statusMessage) {
		this.initialized = initialized;
		this.shutdown = shutdown;
		this.enabled = enabled;
		this.corePoolSize = corePoolSize;
		this.maximumPoolSize = maximumPoolSize;
		this.activeCount = activeCount;
		this.queueSize = queueSize;
		this.completedTaskCount = completedTaskCount;
		this.poolName = poolName;
		this.statusMessage = statusMessage;
	}
	
	public boolean isInitialized() {
		return initialized;
	}
	
	public boolean isShutdown() {
		return shutdown;
	}
	
	public boolean isEnabled() {
		return enabled;
	}
	
	public int getCorePoolSize() {
		return corePoolSize;
	}
	
	public int getMaximumPoolSize() {
		return maximumPoolSize;
	}
	
	public int getActiveCount() {
		return activeCount;
	}
	
	public int getQueueSize() {
		return queueSize;
	}
	
	public long getCompletedTaskCount() {
		return completedTaskCount;
	}
	
	public String getPoolName() {
		return poolName;
	}
	
	public String getStatusMessage() {
		return statusMessage;
	}
	
	/** 检查线程池是否健康 */
	public boolean isHealthy() {
		if (!initialized || shutdown || !enabled) {
			return false;
		}
		
		// 检查线程池使用率
		double threadUsage = maximumPoolSize > 0 ? (double) activeCount / maximumPoolSize : 0;
		if (threadUsage > 0.9) {
			return false; // 线程使用率过高
		}
		
		// 检查队列堆积（假设队列容量，实际应该从配置获取）
		if (queueSize > 800) {
			return false; // 队列堆积严重
		}
		
		return true;
	}
	
	/** 获取使用率信息 */
	public String getUsageInfo() {
		if (!initialized || shutdown || !enabled) {
			return statusMessage;
		}
		
		double threadUsage = maximumPoolSize > 0 ? (double) activeCount / maximumPoolSize : 0;
		return String.format("%s, 线程使用率:%.1f%%", statusMessage, threadUsage * 100);
	}
	
	@Override
	public String toString() {
		return statusMessage;
	}
}
