package org.easitline.common.db;


import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.utils.string.StringUtils;

/**
 * 生成SQL类
 *
 */
public class EasySQL {

	private StringBuffer buf = new StringBuffer();
	
	private List<Object> params = new ArrayList<Object>();
	
	private EasyQuery query=null;
	
	public EasySQL(){
	}
	public EasySQL(String sql){
		buf.append(sql);
	}
	
	public EasySQL(EasyQuery query,String sql){
		this.query=query;
		buf.append(sql);
	}
	public void setRecord(EasyRecord record) {
		record.setSQL(buf, params);
	}
	public void execute(){
		if(query!=null){
			try {
				query.execute(this.getSQL(), this.getParams());
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}else{
			System.out.println("query is not null!");
		}
	}
	
	/**
	 * append一个sql
	 * @param paramValue  参数值，如果这个值为空，则不append
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * 默认跳过参数为空的
	 */
	public EasySQL append(Object paramValue,String sql){
		return append(paramValue,sql,true);
	}
	/**
	 * append一个sql
	 * @param paramValue  参数值
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * @param skipEmpty 是否跳过参数为空的sql
	 */
	public EasySQL append(Object paramValue,String sql,boolean skipEmpty){
		if(skipEmpty){
			if(paramValue!=null&&StringUtils.notBlank(StringUtils.trimToEmpty(paramValue.toString()))){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add(paramValue);
				}
				buf.append(" ").append(sql);
			}
		}else{
			params.add(paramValue);
			buf.append(" ").append(sql);
		}
		return this;
	}
	/**
	 * 
	 * @param paramValue 参数
	 * @param sql SQL语句
	 * @param defaultVal参数为空的时候取默认值
	 * @return
	 */
	public EasySQL append(Object paramValue,String sql,String defaultVal){
		if(paramValue == null||StringUtils.isBlank(StringUtils.trimToEmpty(paramValue.toString()))){
			paramValue = defaultVal;
		}
		return append(paramValue,sql,false);
	}
	/**
	 * 添加排序
	 * @param sortName 需要排序的字段
	 * @param sortType 排序类型 ASC DESC
	 * @return
	 */
	public EasySQL appendSort(String sortName,String sortType){
		return appendSort(sortName,sortType,null);
	}
	public EasySQL appendSort(String sortName,String sortType,String defaultSort){
		if(StringUtils.isBlank(defaultSort)&&StringUtils.notBlank(sortName)){
			buf.append(" ORDER BY ").append(sortName).append(" ").append(sortType);
		}else if(StringUtils.notBlank(defaultSort)&&StringUtils.notBlank(sortName)){
			buf.append(" ORDER BY ").append(sortName).append(" ").append(sortType).append(",").append(defaultSort);
		}else if(StringUtils.notBlank(defaultSort)&&StringUtils.isBlank(sortName)){
			buf.append(" ORDER BY ").append(defaultSort);
		}
		return this;
	}
	
	/**
	 * append sql 不带参数,可用于在sql最后加入order by 等参数
	 * @param sql
	 * @return
	 */
	public EasySQL  append(String sql){
		if(sql!=null) {
			buf.append(" ").append(sql);
		}
		return this;
	}
	
	private EasySQL appendInInternal(Object[] values, String sql) {
		if(values != null){
			int count = values.length;
			if(count==0)return this;
			if(count==1){
				buf.append(" ").append(sql).append(" = ?");
				params.add(values[0]);
				return this;
			}
			StringBuffer buffer=new StringBuffer();
			buffer.append(" ").append(sql).append(" in (");
			for(int i = 0 ;i <count ;i++){
				buffer.append("?,");
			}
			buffer.delete(buffer.length()-1, buffer.length());
			buffer.append(")");
			for(int i = 0 ;i <count ;i++){
				params.add(values[i]);
			}
			buf.append(" ").append(buffer);
	    }
		return this;
	}

	public EasySQL appendIn(int[] paramValues,String sql){
		if(paramValues != null){
			Integer[] values = new Integer[paramValues.length];
			for(int i = 0; i < paramValues.length; i++) {
				values[i] = paramValues[i];
			}
			return appendInInternal(values, sql);
		}
		return this;
	}

	public EasySQL appendIn(String[] paramValues,String sql){
		return appendInInternal(paramValues, sql);
	}
	
	/**
	 * append 左like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add("%"+paramValue);
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	
	/**
	 * append  右like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendRLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add(paramValue+"%");
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	
	
	/**
	 * append like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add("%"+paramValue+"%");
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	/**
	 * 计算有多个少参数
	 * @param str
	 * @param substr
	 * @return
	 */
	private int countParam(String str, String substr) {
		int index = 0;
		int count = 0;
		int fromindex = 0;
		while ((index = str.indexOf(substr, fromindex)) != -1) {
			fromindex = index + substr.length();
			count++;
		}
		return count;
	}
	
	/**
	 * 获得执行的SQL
	 * @return
	 */
	public String getSQL(){
		return buf.toString();
	}
	
	/**
	 * 获得执行的参数
	 * @return
	 */
	public Object[] getParams(){
		return params.toArray();
	}
	
	public StringBuffer getSqlBuf() {
		return buf;
	}
	public List<Object> getParamsList() {
		return params;
	}
	
	public void addParams(Object param){
		params.add(param);
	}
	
	public String toFullSql() {
		String sql = getSQL();
		Object[] params = getParams();
		return SqlHelper.showFullSql(sql, params);
	}
	
	@Deprecated
	public String getFullSq() {
		return toFullSql();
	}
	
	public String getFullSql() {
		return toFullSql();
	}
}
