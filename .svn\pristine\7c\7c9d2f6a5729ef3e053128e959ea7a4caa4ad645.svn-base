package org.easitline.common.db;

import java.sql.SQLException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.helper.ExecutorStatus;
import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.db.log.JDBCWarnLogger;

import com.alibaba.fastjson.JSONObject;

/**
 * SQL监控和告警工具类
 * 提供SQL执行监控、慢SQL告警、执行统计等功能
 *
 * 配置参数：
 * - sql.monitor.queue.capacity: 监控线程池队列容量，默认1000，示例：-Dsql.monitor.queue.capacity=2000
 * - sql.alarm.notice.enabled: SQL告警通知开关，默认false，示例：-Dsql.alarm.notice.enabled=true
 *
 * 框架配置兼容：
 * - sqlAlarmNoticeFlag: SQL告警通知开关（ServerContext配置），示例：sqlAlarmNoticeFlag=1
 *
 * 配置优先级：运行时设置 > JVM环境变量 > 框架配置 > 默认值
 *
 * 使用示例：
 * // 监控状态查询
 * ExecutorStatus status = SqlHelper.getExecutorStatus();
 *
 * // 运行时配置
 * SqlHelper.setSqlAlarmNoticeEnabled(true);
 * SqlHelper.resetSqlAlarmNoticeCache();
 *
 * // 手动添加告警通知
 * SqlHelper.addNotify(executionTime, sql, params, metaData, stackTrace);
 *
 * 环境配置建议：
 * - 开发环境： -Dsql.alarm.notice.enabled=false
 * - 测试环境： -Dsql.alarm.notice.enabled=true -Dsql.monitor.queue.capacity=500
 * - 生产环境： -Dsql.alarm.notice.enabled=true -Dsql.monitor.queue.capacity=2000
 *
 * 线程池配置：
 * - 核心线程数：max(2, CPU核数/2)
 * - 最大线程数：max(4, CPU核数)
 * - 拒绝策略：DiscardOldestPolicy（丢弃最老任务，避免主线程阻塞）
 */
public class SqlHelper {

	private static volatile ExecutorService executor;
	
	/** SQL告警通知开关缓存 */
	private static volatile Boolean enableSendMsgFlag = null;

	/** SQL告警通知开关锁 */
	private static final Object SEND_MSG_LOCK = new Object();

	private static final Object LOCK = new Object();

	/** 队列容量 */
	private static final int QUEUE_CAPACITY = 1000;

	private static volatile boolean shutdownHookRegistered = false;


	/**
	 * 获取或初始化线程池
	 * @return 线程池实例，如果禁用则返回null
	 */
	private static ExecutorService getMonitorExecutor() {
		synchronized (LOCK) {
			// 如果已有可用的线程池，直接返回
			if (executor != null && !executor.isShutdown()) {
				return executor;
			}

			// 只有在需要开启时才注册关闭钩子
			if (!shutdownHookRegistered) {
				registerShutdownHook();
				shutdownHookRegistered = true;
			}

			// 动态计算线程池大小
			int cores = Runtime.getRuntime().availableProcessors();
			int corePoolSize = Math.max(2, cores / 2);
			int maximumPoolSize = Math.max(4, cores);

			executor = new ThreadPoolExecutor(
				corePoolSize, maximumPoolSize,
				60L, TimeUnit.SECONDS,
				new LinkedBlockingQueue<>(QUEUE_CAPACITY),
				new ThreadFactory() {
					private final AtomicInteger counter = new AtomicInteger(1);
					@Override
					public Thread newThread(Runnable r) {
						Thread thread = new Thread(r, "SQL-Monitor-Thread-" + counter.getAndIncrement());
						thread.setDaemon(true);
						return thread;
					}
				},
				new ThreadPoolExecutor.DiscardOldestPolicy() // 丢弃最老的任务，避免主线程阻塞
			);

			JDBCLogger.getLogger().info("SQL监控线程池已初始化 - 核心线程:" + corePoolSize +", 最大线程:" + maximumPoolSize + ", 队列容量:" + QUEUE_CAPACITY);

			return executor;
		}
	}


	/**
	 * 调用告警服务
	 * @param exeTime
	 * @param sql
	 * @param param
	 * @param metaData
	 * @param stackTrace
	 */
	public static void addNotify(long exeTime,String sql,String param,ConnectionMetaData  metaData,String stackTrace) {
	    if(!sendMsgFlag()) {
	    	return;
	    }
		ExecutorService executor = getMonitorExecutor();
	    if (executor == null) {
	        return;
	    }
	    
	    executor.execute(new Runnable() {
	        @Override
	        public void run() {
	            try {
	                JSONObject notifyObj = new JSONObject();
	                notifyObj.put("exeTime", exeTime);
	                notifyObj.put("sql", sql);
	                notifyObj.put("param", param);
	                notifyObj.put("stackTrace", stackTrace);
	                if(metaData!=null) {
	                	notifyObj.put("appName", metaData.getAppName());
	                	notifyObj.put("dsName", metaData.getSysDatasourceName());
	                }
	                // 调用通知服务
	                JDBCLogger.getLogger().info("启动SQL执行超过2S告警通知...");
	                IService service = ServiceContext.getService("SQL_WARN_NOTIFY_SERVICE");
	                if (service != null) {
	                    service.invoke(notifyObj);
	                }
	            } catch (Exception ex) {
	                CoreLogger.getPlatform().error("处理通知消息异常", ex);
	            }
	        }
	    });
	}
	
	public static String showFullSql(String sql, Object[] params) {
		if (sql == null || sql.isEmpty() || params == null || params.length == 0) {
			return sql;
		}
		
		// 预估容量为SQL长度 + 参数数量 * 20
		StringBuilder result = new StringBuilder(sql.length() + params.length * 20);
		int paramIndex = 0;
		
		try {
			char[] sqlChars = sql.toCharArray(); // 避免重复charAt调用
			for (char c : sqlChars) {
				if (c == '?' && paramIndex < params.length) {
					appendParam(result, params[paramIndex++]);
				} else {
					result.append(c);
				}
			}
			return result.toString();
		} catch (Exception e) {
			return String.format("Original SQL: %s, Params: %s", sql, paramToString(params));
		}
	}
	
	private static void appendParam(StringBuilder sb, Object param) {
		if (param == null) {
			sb.append("NULL");
			return;
		}
		
		if (param instanceof String || param instanceof java.util.Date) {
			sb.append('\'').append(param).append('\'');
		} else {
			sb.append(param);
		}
	}
	
	public static String paramToString(Object[] params){
		if(params == null) return "{}";
		StringBuilder buf = new StringBuilder(params.length * 16);
		buf.append("{");
		for (int i = 0; i < params.length; i++) {
			if(i == params.length -1) 
			   buf.append(params[i]);
			else 
				buf.append(params[i]).append(",");
		}
		buf.append("}");
		return buf.toString();
	}
	
	public static void errorLogOut(Logger appLogger,SQLException ex,String sql,Object[] params,ConnectionMetaData  metaData){
		StringBuilder msg = new StringBuilder();
		msg.append("Error! SQL exception cause:"+ex.getMessage()+",sql:"+sql+",params["+paramToString(params)+"]");
		if (metaData!=null) {
			msg.append(",source["+metaData.getAppName()+"|");
			msg.append(metaData.getSysDatasourceName()+"]");
		}
		String fullSql = showFullSql(sql, params);
		JDBCErrorLogger.getLogger().error("SQLException："+ex.getMessage()+"["+fullSql+"]");
		JDBCErrorLogger.getLogger().error(msg.toString(),ex);
		if(appLogger!=null){
			appLogger.error(msg.toString(),ex);
		}
		if(metaData!=null) {
			sendSqlErrorLogMsg(fullSql, ex.getMessage(), metaData.getAppName(), metaData.getSysDatasourceName());
		}else {
			sendSqlErrorLogMsg(fullSql, ex.getMessage(),"null","null");
		}
	}
	
	public static StackTraceElement getStackTrace(StackTraceElement[] stackTrace){
		if (stackTrace == null || stackTrace.length == 0) {
			return null;
		}
		for (StackTraceElement element : stackTrace) {
			String className = element.getClassName();
			// 跳过框架相关的包
			if (!className.startsWith("org.easitline") && 
				!className.startsWith("java.") && 
				!className.startsWith("sun.") &&
				!className.startsWith("javax.")) {
				return element;
			}
		}
		return null;
	}
	
	public static void timeoutLogOut(Logger appLogger, long exetime, String sql, Object[] params,ConnectionMetaData  metaData) {
		String parmaString = paramToString(params);		
		String trace = "";
		StackTraceElement  element = getStackTrace(Thread.currentThread().getStackTrace());
		if(element!=null) {
			trace = element.toString();
		}
		StringBuilder msg = new StringBuilder();
		msg.append("["+exetime+"ms] "+sql+",params["+parmaString+"]");
		if(metaData!=null){
			msg.append(",source["+metaData.getAppName()+"|");
			msg.append(metaData.getSysDatasourceName()+"]");
		}
		msg.append(" - "+trace);
		if (exetime > 2000) {
			JDBCWarnLogger.getDangerLogger().warn(msg.toString());
			if (appLogger != null) { 
				appLogger.warn(msg.toString());
			}
			addNotify(exetime,sql,parmaString,metaData,trace);
			if(exetime>10000) {
				if(metaData!=null) {
					sendSqlSlowLogMsg(showFullSql(sql, params), exetime, metaData.getAppName(),metaData.getSysDatasourceName());
				}else {
					sendSqlSlowLogMsg(showFullSql(sql, params), exetime,"null","null");
				}
		    }
		}else{
			if (appLogger != null) {
			   appLogger.info(msg.toString());
			}

		}
		JDBCLogger.getLogger().info(msg.toString());
	}
	
	
	public static void listSizeLogOut(Logger appLogger,long recordCount,String sql,Object[] params){
		String parmaString = paramToString(params);
		String msg = "[list.size()->"+recordCount+"] "+sql+",params["+parmaString+"] - "+getStackTrace(Thread.currentThread().getStackTrace())+" ";
		JDBCLogger.getLogger().warn(msg);
		if(appLogger != null) appLogger.warn(msg);
	}
	
	/**
	 * 获取SQL告警通知开关状态（统一配置管理）
	 * 优先级：JVM环境变量 > ServerContext配置 > 默认值
	 */
	private static boolean sendMsgFlag() {
		if (enableSendMsgFlag == null) {
			synchronized (SEND_MSG_LOCK) {
				if (enableSendMsgFlag == null) {
					// 优先从JVM环境变量读取
					String jvmFlag = System.getProperty("sql.alarm.notice.enabled");
					if (jvmFlag != null) {
						enableSendMsgFlag = "true".equalsIgnoreCase(jvmFlag);
					} else {
						// 回退到ServerContext配置
						enableSendMsgFlag = "1".equals(ServerContext.getProperties("sqlAlarmNoticeFlag", "0"));
					}
				}
			}
		}
		return enableSendMsgFlag;
	}

	/**
	 * 设置SQL告警通知开关（运行时动态设置）
	 * @param enabled 是否启用SQL告警通知
	 */
	public static void setSqlAlarmNoticeEnabled(boolean enabled) {
		synchronized (SEND_MSG_LOCK) {
			enableSendMsgFlag = enabled;
		}
	}

	/**
	 * 重置SQL告警通知缓存（用于配置更改后刷新）
	 */
	public static void resetSqlAlarmNoticeCache() {
		synchronized (SEND_MSG_LOCK) {
			enableSendMsgFlag = null;
		}
	}
	
	
	/**
	 * 慢SQL通知
	 * @param sql
	 * @param time
	 * @param appName
	 * @param dsName
	 */
	private static void sendSqlSlowLogMsg(String sql, long time, String appName, String dsName) {
		if(!sendMsgFlag()) {
		    return;
		}
		ExecutorService executor = getMonitorExecutor();
		if (executor == null) {
			return;
		}
		
		executor.execute(new Runnable() {
			@Override
			public void run() {
				try {
					JSONObject params = new JSONObject();
					params.put("type", "slow");
					params.put("sql", sql);
					params.put("time", time);
					params.put("appName", appName);
					params.put("dsName", dsName);
					
					LogEngine.getLogger("easitline-sql-slow").error(params.toJSONString());
					
					IService service = ServiceContext.getService("SQL-MONITOR-SERVICE");
					if (service != null) {
						service.invoke(params);
					}
				} catch (Exception ex) {
					CoreLogger.getPlatform().error("处理慢SQL监控消息异常", ex);
				}
			}
		});
	}
	
	/**
	 * 错误SQL通知
	 * @param sql
	 * @param errorMsg
	 * @param appName
	 * @param dsName
	 */
	private static void sendSqlErrorLogMsg(String sql, String errorMsg, String appName, String dsName) {
		if(!sendMsgFlag()) {
		    return;
		}

		ExecutorService executor = getMonitorExecutor();
		if (executor == null) {
			return;
		}
		
		executor.execute(new Runnable() {
			@Override
			public void run() {
				try {
					JSONObject params = new JSONObject();
					params.put("type", "error");
					params.put("sql", sql);
					params.put("errorMsg", errorMsg);
					params.put("appName", appName);
					params.put("dsName", dsName);
					
					LogEngine.getLogger("easitline-sql-error").error(params.toJSONString());
					
					IService service = ServiceContext.getService("SQL-MONITOR-SERVICE");
					if (service != null) {
						service.invoke(params);
					}
				} catch (Exception ex) {
					CoreLogger.getPlatform().error("处理SQL错误监控消息异常", ex);
				}
			}
		});
	}

	/** 注册JVM关闭钩子 */
	private static void registerShutdownHook() {
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			synchronized (LOCK) {
				if (executor != null && !executor.isShutdown()) {
					JDBCLogger.getLogger().info("正在关闭SQL监控线程池...");
					shutdown();
				}
			}
		}, "SQL-Monitor-Shutdown-Hook"));
	}

	private static void shutdown() {
		if (executor != null && !executor.isShutdown()) {
			executor.shutdown();
			try {
				if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
					executor.shutdownNow();
					if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
						JDBCLogger.getLogger().warn("SQL监控线程池无法正常关闭");
					}
				}
			} catch (InterruptedException e) {
				executor.shutdownNow();
				Thread.currentThread().interrupt();
			} finally {
				executor = null;
			}
		}
	}

	/** 获取线程池状态信息 */
	public static ExecutorStatus getExecutorStatus() {
		synchronized (LOCK) {
			if (!sendMsgFlag()) {
				return ExecutorStatus.disabled("SQL监控线程池");
			}
			if (executor == null) {
				return ExecutorStatus.uninitialized("SQL监控线程池");
			}
			if (executor.isShutdown()) {
				return ExecutorStatus.shutdown("SQL监控线程池");
			}
			if (executor instanceof ThreadPoolExecutor) {
				return ExecutorStatus.fromThreadPoolExecutor((ThreadPoolExecutor) executor, "SQL监控线程池");
			}
			return ExecutorStatus.uninitialized("SQL监控线程池");
		}
	}

	/** 获取线程池状态信息（字符串格式，保持向后兼容） */
	public static String getExecutorStatusString() {
		return getExecutorStatus().toString();
	}

	/** 强制关闭线程池 */
	public static void forceShutdown() {
		synchronized (LOCK) {
			if (executor != null && !executor.isShutdown()) {
				JDBCLogger.getLogger().info("强制关闭SQL监控线程池");
				shutdown();
			}
		}
	}

}

