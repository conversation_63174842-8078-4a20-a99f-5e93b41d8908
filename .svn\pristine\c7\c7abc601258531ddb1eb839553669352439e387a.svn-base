package org.easitline.common.db.helper;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.SQLTimeoutException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.db.log.JDBCLogger;

/**
 * 数据库超时控制工具类
 * 提供客户端级别的SQL执行超时控制，双重保护机制
 *
 * 配置参数：
 * - enable.timeout.control: 超时控制开关，默认true，示例：-Denable.timeout.control=false
 * - db.timeout.queue.capacity: 线程池队列容量，默认500，示例：-Ddb.timeout.queue.capacity=1000
 *
 * 配置优先级：运行时设置 > JVM环境变量 > 默认值
 *
 * 使用示例：
 * // 基本使用（EasyQueryImpl中自动调用）
 * EasyQueryImpl query = new EasyQueryImpl("datasourceName");
 * query.setTimeout(30);
 * List<EasyRow> results = query.queryForList("SELECT * FROM table");
 *
 * // 直接使用
 * TimeoutControlHelper.executeWithTimeout(stmt, 30, () -> {
 *     return stmt.executeQuery();
 * });
 *
 * // 配置管理
 * TimeoutControlHelper.setTimeoutControlEnabled(false);  // 运行时禁用
 * boolean enabled = TimeoutControlHelper.isTimeoutControlEnabled();
 * ExecutorStatus status = TimeoutControlHelper.getTimeoutExecutorStatus();
 *
 * 环境配置建议：
 * - 开发环境：-Denable.timeout.control=false（便于调试）
 * - 测试环境：-Denable.timeout.control=true -Ddb.timeout.queue.capacity=200
 * - 生产环境：-Denable.timeout.control=true -Ddb.timeout.queue.capacity=1000
 */
public class TimeoutControlHelper {

	/** 超时控制开关缓存 */
	private static volatile Boolean enableTimeoutControl = null;
	
	/** 超时控制开关锁 */
	private static final Object TIMEOUT_CONTROL_LOCK = new Object();

	/** 用于超时控制的线程池 */
	private static volatile ExecutorService timeoutExecutor;
	
	/** 线程池初始化锁 */
	private static final Object EXECUTOR_LOCK = new Object();

	/** 是否已注册关闭钩子 */
	private static volatile boolean shutdownHookRegistered = false;

	/**
	 * SQL执行器接口
	 */
	@FunctionalInterface
	public interface SqlExecutor<T> {
		T execute() throws SQLException;
	}

	/**
	 * 获取超时控制开关状态（从JVM环境变量读取）
	 * @return 是否启用超时控制
	 */
	public static boolean isTimeoutControlEnabled() {
		if (enableTimeoutControl == null) {
			synchronized (TIMEOUT_CONTROL_LOCK) {
				if (enableTimeoutControl == null) {
					String timeoutEnv = System.getProperty("enable.timeout.control", "true");
					enableTimeoutControl = !"false".equalsIgnoreCase(timeoutEnv);
				}
			}
		}
		return enableTimeoutControl;
	}

	/**
	 * 设置超时控制开关（运行时动态设置，会覆盖JVM环境变量）
	 * @param enabled 是否启用超时控制
	 */
	public static void setTimeoutControlEnabled(boolean enabled) {
		synchronized (TIMEOUT_CONTROL_LOCK) {
			enableTimeoutControl = enabled;
		}
	}

	/**
	 * 重置超时控制缓存（用于配置更改后刷新）
	 */
	public static void resetTimeoutControlCache() {
		synchronized (TIMEOUT_CONTROL_LOCK) {
			enableTimeoutControl = null;
		}
	}

	/**
	 * 获取超时控制线程池（公共方法）
	 * @return 线程池实例，如果禁用则返回null
	 */
	public static ExecutorService getTimeoutExecutor() {
		if (!isTimeoutControlEnabled()) {
			return null;
		}
		return getTimeoutExecutorInternal();
	}

	/**
	 * 懒加载获取超时控制线程池（内部方法）
	 */
	private static ExecutorService getTimeoutExecutorInternal() {
		if (timeoutExecutor == null) {
			synchronized (EXECUTOR_LOCK) {
				if (timeoutExecutor == null) {
					int cores = Runtime.getRuntime().availableProcessors();
					// 保守的线程池配置：核心线程数=CPU核数，最大线程数=CPU核数*2
					int corePoolSize = cores;
					int maximumPoolSize = cores * 2;
					// 队列大小可通过JVM参数调整，默认500
					int queueCapacity = Integer.parseInt(System.getProperty("db.timeout.queue.capacity", "500"));
					
					timeoutExecutor = new ThreadPoolExecutor(
						corePoolSize, maximumPoolSize,
						60L, TimeUnit.SECONDS,
						new LinkedBlockingQueue<>(queueCapacity),
						new ThreadFactory() {
							private final AtomicInteger counter = new AtomicInteger(1);
							@Override
							public Thread newThread(Runnable r) {
								Thread thread = new Thread(r, "DB-Timeout-" + counter.getAndIncrement());
								thread.setDaemon(true);
								return thread;
							}
						},
						new ThreadPoolExecutor.AbortPolicy() // 明确拒绝，便于降级处理
					);
					JDBCLogger.getLogger().info("数据库超时控制线程池已初始化 - 核心线程:" + corePoolSize + ", 最大线程:" + maximumPoolSize + ", 队列容量:" + queueCapacity);

					// 注册JVM关闭钩子
					registerShutdownHook();
				}
			}
		}
		return timeoutExecutor;
	}

	/**
	 * 注册JVM关闭钩子
	 */
	private static void registerShutdownHook() {
		if (!shutdownHookRegistered) {
			synchronized (EXECUTOR_LOCK) {
				if (!shutdownHookRegistered) {
					Runtime.getRuntime().addShutdownHook(new Thread(() -> {
						JDBCLogger.getLogger().info("JVM关闭，正在清理数据库超时控制线程池...");
						shutdownTimeoutExecutor();
					}, "DB-Timeout-Shutdown-Hook"));
					shutdownHookRegistered = true;
					JDBCLogger.getLogger().info("已注册数据库超时控制线程池关闭钩子");
				}
			}
		}
	}

	/**
	 * 关闭超时控制线程池（用于应用关闭时的资源清理）
	 */
	public static void shutdownTimeoutExecutor() {
		synchronized (EXECUTOR_LOCK) {
			if (timeoutExecutor != null && !timeoutExecutor.isShutdown()) {
				JDBCLogger.getLogger().info("正在关闭数据库超时控制线程池...");
				timeoutExecutor.shutdown();
				try {
					if (!timeoutExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
						JDBCLogger.getLogger().warn("线程池未能在5秒内正常关闭，强制关闭");
						timeoutExecutor.shutdownNow();
						if (!timeoutExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
							JDBCErrorLogger.getLogger().error("线程池强制关闭失败");
						}
					}
				} catch (InterruptedException e) {
					Thread.currentThread().interrupt();
					timeoutExecutor.shutdownNow();
				} finally {
					timeoutExecutor = null;
				}
			}
		}
	}

	/**
	 * 使用客户端超时控制执行SQL操作
	 * 双重保护：客户端stmt.cancel() + 数据库setQueryTimeout
	 *
	 * @param stmt SQL语句对象
	 * @param timeoutSeconds 超时时间（秒）
	 * @param executor SQL执行器
	 * @return 执行结果
	 * @throws SQLException SQL异常
	 */
	public static <T> T executeWithTimeout(PreparedStatement stmt, int timeoutSeconds, SqlExecutor<T> executor) throws SQLException {
		// 参数验证
		if (stmt == null) {
			throw new IllegalArgumentException("PreparedStatement不能为null");
		}
		if (executor == null) {
			throw new IllegalArgumentException("SqlExecutor不能为null");
		}
		if (timeoutSeconds <= 0) {
			throw new IllegalArgumentException("超时时间必须大于0秒，当前值: " + timeoutSeconds);
		}
		
		// 如果禁用超时控制，直接执行
		if (!isTimeoutControlEnabled()) {
			return executor.execute();
		}

		Future<T> future;
		try {
			future = getTimeoutExecutorInternal().submit(() -> {
				try {
					return executor.execute();
				} catch (SQLException e) {
					// 不在这里记录日志，让外层统一处理
					throw new RuntimeException(e);
				}
			});
		} catch (java.util.concurrent.RejectedExecutionException e) {
			// 线程池满载时的降级处理：直接在当前线程执行，但记录警告
			JDBCErrorLogger.getLogger().warn("数据库超时控制线程池满载，降级到当前线程执行");
			JDBCErrorLogger.getLogger().warn("线程池状态: " + getTimeoutExecutorStatusString());
			return executor.execute();
		}

		try {
			// 使用客户端超时控制，比数据库超时稍短
			return future.get(timeoutSeconds, TimeUnit.SECONDS);
		} catch (TimeoutException e) {
			try {
				stmt.cancel();
				JDBCErrorLogger.getLogger().warn("客户端超时，已调用stmt.cancel()取消数据库查询，超时时间: " + timeoutSeconds + "秒");
			} catch (SQLException cancelEx) {
				JDBCErrorLogger.getLogger().warn("调用stmt.cancel()时发生异常: " + cancelEx.getMessage(), cancelEx);
			}
			// 然后中断Java线程
			future.cancel(true);
			throw new SQLTimeoutException("客户端强制终止查询，超时时间: " + timeoutSeconds + "秒");
		} catch (InterruptedException e) {
			// 恢复中断状态
			Thread.currentThread().interrupt();
			throw new SQLException("SQL执行被中断", e);
		} catch (Exception e) {
			JDBCErrorLogger.getLogger().error("SQL执行异常: " + e.getMessage(), e);
			if (e.getCause() instanceof SQLException) {
				throw (SQLException) e.getCause();
			}
			throw new SQLException("执行SQL时发生异常", e);
		}
	}

	/**
	 * 获取线程池状态信息（用于监控）
	 * @return 线程池状态对象
	 */
	public static ExecutorStatus getTimeoutExecutorStatus() {
		if (!isTimeoutControlEnabled()) {
			return ExecutorStatus.disabled("DB超时控制线程池");
		}

		ExecutorService executor = timeoutExecutor; // 避免重复初始化
		if (executor == null) {
			return ExecutorStatus.uninitialized("DB超时控制线程池");
		}
		if (executor.isShutdown()) {
			return ExecutorStatus.shutdown("DB超时控制线程池");
		}
		if (executor instanceof ThreadPoolExecutor) {
			return ExecutorStatus.fromThreadPoolExecutor((ThreadPoolExecutor) executor, "DB超时控制线程池");
		}
		return ExecutorStatus.uninitialized("DB超时控制线程池");
	}

	/**
	 * 获取线程池状态信息（字符串格式，保持向后兼容）
	 * @return 线程池状态描述
	 */
	public static String getTimeoutExecutorStatusString() {
		return getTimeoutExecutorStatus().toString();
	}
}
