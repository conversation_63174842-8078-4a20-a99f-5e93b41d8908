package org.easitline.common.db.helper;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.log.JDBCLogger;

/** 数据库连接和事务管理工具类 */
public class ConnectionHelper {

	/** 开始数据库事务 */
	public static Connection beginTransaction(ConnectionMetaData metaData, Connection transactionConnection) throws SQLException {
		if (transactionConnection != null) {
			return transactionConnection;
		}

		if (metaData == null) {
			throw new SQLException("数据库连接元数据未初始化，无法开始事务");
		}

		Connection connection = metaData.getConnection();
		if (connection != null && !connection.isClosed()) {
			connection.setAutoCommit(false);
		}
		JDBCLogger.getLogger().info("beginTransaction:"+metaData.getAppName()+","+metaData.getSysDatasourceName());
		return connection;
	}

	/** 提交数据库事务 */
	public static void commitTransaction(Connection connection) throws SQLException {
		if (connection == null) {
			throw new SQLException("没有活动的事务可以提交");
		}

		connection.commit();
		connection.setAutoCommit(true);
	}

	/** 回滚数据库事务 */
	public static void rollbackTransaction(Connection connection) throws SQLException {
		if (connection == null) {
			throw new SQLException("没有活动的事务可以回滚");
		}

		connection.rollback();
		connection.setAutoCommit(true);
	}

	/** 获取数据库连接 */
	public static Connection getConnection(ConnectionMetaData metaData, Connection transactionConnection) throws SQLException {
		if (transactionConnection != null) {
			return transactionConnection;
		}

		if (metaData == null) {
			throw new SQLException("ConnectionMetaData not initialized and no cached connection available");
		}

		return metaData.getConnection();
	}

	/** 关闭数据库连接 */
	public static void closeConnection(Connection connection, Logger logger) {
		try {
			if (connection != null) {
				connection.close();
			}
		} catch (Exception e) {
			if (logger != null) {
				logger.error(e, e);
			} else {
				JDBCLogger.getLogger().error(e, e);
			}
		}
	}

	/** 释放数据库资源 */
	public static void closeResources(ResultSet rs, Statement stmt, Connection conn,
			Connection transactionConnection, Logger logger) {
		// 关闭ResultSet
		closeQuietly(rs, "ResultSet", logger);

		// 关闭Statement
		closeQuietly(stmt, "Statement", logger);

		// 如果存在事务连接，检查是否为同一连接
		if (transactionConnection != null) {
			// 如果conn和transactionConnection是同一个对象，不关闭连接
			if (conn == transactionConnection) {
				return;
			}
			// 如果不是同一个对象，仍然关闭conn（可能是临时连接）
		}

		// 关闭Connection
		closeQuietly(conn, "Connection", logger);
	}

	/** 安全关闭资源的通用方法 */
	private static void closeQuietly(AutoCloseable resource, String resourceType, Logger logger) {
		if (resource != null) {
			try {
				resource.close();
			} catch (Exception ex) {
				String errorMsg = "关闭" + resourceType + "时发生异常: " + ex.getMessage();
				if (logger != null) {
					logger.error(errorMsg, ex);
				} else {
					JDBCLogger.getLogger().error(errorMsg, ex);
				}
			}
		}
	}
}
