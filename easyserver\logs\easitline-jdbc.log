2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:No suitable driver found for **************************************
java.sql.SQLException: No suitable driver found for **************************************
	at java.sql.DriverManager.getConnection(DriverManager.java:689)
	at java.sql.DriverManager.getConnection(DriverManager.java:247)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:69)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:666)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at org.easitline.common.core.context.ServerContext.reload(ServerContext.java:294)
	at org.easitline.common.core.context.ServerContext.init(ServerContext.java:284)
	at org.easitline.common.core.context.ServerContext.<clinit>(ServerContext.java:37)
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:55)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:No suitable driver found for **************************************
java.sql.SQLException: No suitable driver found for **************************************
	at java.sql.DriverManager.getConnection(DriverManager.java:689)
	at java.sql.DriverManager.getConnection(DriverManager.java:247)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:69)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.setPagination(EasyQueryImpl.java:647)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:669)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at org.easitline.common.core.context.ServerContext.reload(ServerContext.java:294)
	at org.easitline.common.core.context.ServerContext.init(ServerContext.java:284)
	at org.easitline.common.core.context.ServerContext.<clinit>(ServerContext.java:37)
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:55)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[168ms] select * from  EASI_CONF ,params[{}],source[null|null] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:No suitable driver found for **************************************
java.sql.SQLException: No suitable driver found for **************************************
	at java.sql.DriverManager.getConnection(DriverManager.java:689)
	at java.sql.DriverManager.getConnection(DriverManager.java:247)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:69)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:666)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:55)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:No suitable driver found for **************************************
java.sql.SQLException: No suitable driver found for **************************************
	at java.sql.DriverManager.getConnection(DriverManager.java:689)
	at java.sql.DriverManager.getConnection(DriverManager.java:247)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:69)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.setPagination(EasyQueryImpl.java:647)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:669)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at org.easitline.common.core.EasyPool.initPools(EasyPool.java:55)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:255)
	at org.easitline.common.core.EasyPool.<init>(EasyPool.java:31)
	at org.easitline.common.core.EasyPool$Holder.<clinit>(EasyPool.java:41)
	at org.easitline.common.core.EasyPool.getInstance(EasyPool.java:244)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.helper.ConnectionHelper.getConnection(ConnectionHelper.java:63)
	at org.easitline.common.db.impl.EasyQueryImpl.getConnection(EasyQueryImpl.java:121)
	at org.easitline.common.db.impl.EasyQueryImpl.execute(EasyQueryImpl.java:355)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[6ms] select * from EASI_DS_INFO ,params[{}],source[null|null] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[292ms] CALL long_running_procedure(),params[{}],source[null|your_datasource_name] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testExecuteTimeout(TimeoutCancelTest.java:92)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
java.sql.SQLException: EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
	at org.easitline.common.core.EasyPool.getDruidDatasource(EasyPool.java:292)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:666)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testClientTimeout(TimeoutCancelTest.java:28)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
java.sql.SQLException: EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
	at org.easitline.common.core.EasyPool.getDruidDatasource(EasyPool.java:292)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.setPagination(EasyQueryImpl.java:647)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:669)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:726)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:624)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:337)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testClientTimeout(TimeoutCancelTest.java:28)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[3ms] SELECT SLEEP(10),params[{}],source[null|your_datasource_name] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testClientTimeout(TimeoutCancelTest.java:28)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
2025-08-06 16:19:56	ERROR	[easitline-jdbc:easitline-jdbc]	getDriverType() exception,cause:EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
java.sql.SQLException: EasyEngine->获取系统数据源失败，原因：未找到系统数据源[your_datasource_name]配置信息！
	at org.easitline.common.core.EasyPool.getDruidDatasource(EasyPool.java:292)
	at org.easitline.common.db.ConnectionMetaData.getDruidDataSource(ConnectionMetaData.java:84)
	at org.easitline.common.db.ConnectionMetaData.getConnection(ConnectionMetaData.java:71)
	at org.easitline.common.db.ConnectionMetaData.getDriverType(ConnectionMetaData.java:111)
	at org.easitline.common.db.impl.EasyQueryImpl.getTypes(EasyQueryImpl.java:653)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForList(EasyQueryImpl.java:666)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForRow(EasyQueryImpl.java:731)
	at org.easitline.common.db.impl.EasyQueryImpl.queryForRow(EasyQueryImpl.java:344)
	at test.java.org.easitline.common.db.impl.TimeoutCancelTest.testDualProtection(TimeoutCancelTest.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.executeTestMethod(PojoTestSetExecutor.java:104)
	at org.apache.maven.surefire.junit.PojoTestSetExecutor.execute(PojoTestSetExecutor.java:63)
	at org.apache.maven.surefire.junit.JUnit3Provider.executeTestSet(JUnit3Provider.java:131)
	at org.apache.maven.surefire.junit.JUnit3Provider.invoke(JUnit3Provider.java:93)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[1ms] SELECT SLEEP(4),params[{}],source[null|your_datasource_name] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testDualProtection(TimeoutCancelTest.java:55)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
2025-08-06 16:19:56	INFO	[easitline-jdbc:easitline-jdbc]	[0ms] UPDATE test_table SET col1 = SLEEP(5) WHERE id = ?,params[{1}],source[null|your_datasource_name] - test.java.org.easitline.common.db.impl.TimeoutCancelTest.testUpdateTimeout(TimeoutCancelTest.java:76)	 at org.easitline.common.db.SqlHelper.timeoutLogOut(SqlHelper.java:224)	
