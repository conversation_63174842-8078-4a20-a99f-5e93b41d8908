package org.easitline.common.db.helper;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;

import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

/**
 * SQL构建工具类
 * 提供SQL语句构建、参数设置、SQL Hint等功能
 *
 * 配置参数：
 * - use.sql.hint: SQL Hint功能开关，默认true，示例：-Duse.sql.hint=false
 *
 * 配置优先级：JVM环境变量 > 默认值
 *
 * 使用示例：
 * // SQL参数设置
 * SqlBuilderHelper.setParameter(stmt, 1, "value", String.class);
 *
 * // SQL构建
 * String sql = SqlBuilderHelper.buildSelectSql(tableName, columns, conditions);
 *
 * // 配置管理
 * SqlBuilderHelper.resetSqlHintCache();  // 重置SQL Hint缓存
 *
 * 环境配置建议：
 * - 开发环境：-Duse.sql.hint=false（简化SQL，便于调试）
 * - 测试环境：-Duse.sql.hint=true（测试SQL优化效果）
 * - 生产环境：-Duse.sql.hint=true（启用SQL优化提示）
 */
public class SqlBuilderHelper {
	
	
	/**
	 * 设定查询参数
	 * 
	 * @param stmt
	 * @param index
	 * @param paramObj
	 * @throws SQLException
	 */
	public static void setParam(PreparedStatement stmt, int index, Object paramObj) throws SQLException {
		if (paramObj instanceof Integer) {
			stmt.setInt(index, ((Integer) paramObj).intValue());
		} else if (paramObj instanceof Long) {
			stmt.setLong(index, ((Long) paramObj).longValue());
		} else if (paramObj instanceof Double) {
			stmt.setDouble(index, ((Double) paramObj).doubleValue());
		} else if (paramObj instanceof java.sql.Date) {
			stmt.setDate(index, (java.sql.Date) paramObj);
		} else if (paramObj instanceof java.sql.Timestamp) {
			stmt.setTimestamp(index, (java.sql.Timestamp) paramObj);
		} else if (paramObj instanceof Float) {
			stmt.setFloat(index, ((Float) paramObj).floatValue());
		} else if (paramObj instanceof String) {
			stmt.setString(index, (String) paramObj);
		} else {
			stmt.setObject(index, paramObj);
		}
	}
	
	
	public static String forDbFindById(String tableName, String[] pKeys,String columns) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);
		StringBuilder sql = new StringBuilder("select ");
		columns = columns.trim();
		if ("*".equals(columns)) {
			sql.append("*");
		}
		else {
			String[] arr = columns.split(",");
			for (int i=0; i<arr.length; i++) {
				if (i > 0) {
					sql.append(",");
				}
				sql.append("").append(arr[i].trim()).append("");
			}
		}
		sql.append(" from ");
		sql.append(tableName);
		sql.append(" where ");
		
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
		}
		return sql.toString();
	}
	
	public static StringBuilder forDbDeleteById(EasyRecord record) {
		String[] pKeys=record.getPrimaryKeys();
		trimPrimaryKeys(pKeys);
		StringBuilder sql = new StringBuilder("delete from ").append(record.getTableName()).append(" where ");
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
		}
		String condition = record.getCondition();
		if(condition!=null) {
			sql.append(condition);
		}
		return sql;
	}
	
	public static void trimPrimaryKeys(String[] pKeys) {
		if(pKeys!=null){
			for (int i=0; i<pKeys.length; i++) {
				pKeys[i] = pKeys[i].toUpperCase().trim();
			}
		}
	}
	/**
	 * Do not delete the String[] pKeys parameter, the element of pKeys needs to trim()
	 */
	public static void forDbSave(String tableName, String[] pKeys, EasyRecord record, StringBuilder sql, List<Object> paras) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);	// important
		
		sql.append("insert into ");
		sql.append(tableName).append("(");
		StringBuilder temp = new StringBuilder();
		temp.append(") values(");
		
		EasySQL easySQL=record.getEasySQL();
		if(easySQL!=null){
			sql.append(easySQL.getSQL());
			paras.addAll(Arrays.asList(easySQL.getParams()));
		}
		
		String[] nullKeys = record.getNullKeys();
		if(nullKeys!=null) {
			for(int i=0,len=nullKeys.length;i<len;i++ ) {
				String key = nullKeys[i];
				record.remove(key.toUpperCase());
				sql.append(key);
				if(len!=i+1) {
					sql.append(", ");
					temp.append("?, ");
				}else {
					temp.append("? ");
				}
				paras.add(null);
			}
		}
		
		for (Entry<String, Object> e: record.getColumns().entrySet()) {
			if (paras.size() > 0) {
				sql.append(", ");
				temp.append(", ");
			}
			sql.append("").append(e.getKey()).append("");
			temp.append("?");
			paras.add(e.getValue());
		}
		sql.append(temp.toString()).append(")");
	}
	
	public static void forDbUpdate(String tableName, String[] pKeys, EasyRecord record, StringBuilder sql, List<Object> paras) {
		tableName = tableName.trim();
		trimPrimaryKeys(pKeys);
		
		sql.append("update ").append(tableName).append(" set ");
		EasySQL easySQL=record.getEasySQL();
		if(easySQL!=null){
			sql.append(easySQL.getSQL());
			paras.addAll(Arrays.asList(easySQL.getParams()));
		}
		String[] nullKeys = record.getNullKeys();
		if(nullKeys!=null) {
			for(int i=0,len=nullKeys.length;i<len;i++ ) {
				String key = nullKeys[i];
				record.remove(key.toUpperCase());
				sql.append("").append(key).append(" = ? ");
				if(len!=i+1) {
					sql.append(", ");
				}
				paras.add(null);
			}
		}
		
		for (Entry<String, Object> e: record.getColumns().entrySet()) {
			String colName = e.getKey();
			if (!record.isPrimaryKey(colName, pKeys)) {
				if (paras.size() > 0) {
					sql.append(", ");
				}
				sql.append("").append(colName).append(" = ? ");
				paras.add(e.getValue());
				
			}
		}
		sql.append(" where ");
		for (int i=0; i<pKeys.length; i++) {
			if (i > 0) {
				sql.append(" and ");
			}
			sql.append("").append(pKeys[i]).append(" = ? ");
			paras.add(record.get(pKeys[i]));
		}
		String condition = record.getCondition();
		if(condition!=null) {
			sql.append(condition);
		}
	}

	/** SQL Hint 开关缓存 */
	private static volatile Boolean useSqlHint = null;
	private static final Object HINT_LOCK = new Object();

	/** 获取SQL Hint开关状态 */
	private static boolean isUseSqlHint() {
		if (useSqlHint == null) {
			synchronized (HINT_LOCK) {
				if (useSqlHint == null) {
					String hintEnv = System.getProperty("use.sql.hint", "true");
					useSqlHint = !"false".equalsIgnoreCase(hintEnv);
				}
			}
		}
		return useSqlHint;
	}

	/** 重置SQL Hint缓存（用于配置更改后刷新） */
	public static void resetSqlHintCache() {
		synchronized (HINT_LOCK) {
			useSqlHint = null;
		}
	}

	/** 针对不同的数据库类型，设置sql的执行超时时间，通过增加hint来实现 */
	public static String addQueryTimeoutHint(String sql, DBTypes dbType, int timeoutSeconds) {
	    if (sql == null || sql.isEmpty()) return sql;

	    String trimmedSql = sql.trim();
	    if (trimmedSql.isEmpty()) return sql;

	    // 检查是否启用SQL Hint
	    if (!isUseSqlHint()) {
	        return sql;
	    }

	    String hint = null;
	    int timeoutMs = timeoutSeconds * 1000;

	    switch (dbType) {
	        case MYSQL:
	            hint = String.format("/*+ MAX_EXECUTION_TIME(%d) */", timeoutMs);
	            break;
	        default:
	            return sql; // 不支持的数据库类型，直接返回原SQL
	    }
	    String modified = trimmedSql.replaceFirst("(?i)^\\s*select\\s+", "select " + hint + " ");
	    return modified.equals(trimmedSql) ? sql : modified;
	}
}
