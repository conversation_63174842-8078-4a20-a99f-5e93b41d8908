# executeWithTimeout 迁移示例

## 迁移前后对比

### 迁移前 (EasyQueryImpl)
```java
// 原有实现 - 在EasyQueryImpl中
private <T> T executeWithTimeout(PreparedStatement stmt, SqlExecutor<T> executor) throws SQLException {
    Future<T> future = timeoutExecutor.submit(() -> {
        try {
            return executor.execute();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    });

    try {
        return future.get(this.timeout, TimeUnit.SECONDS);
    } catch (TimeoutException e) {
        try {
            stmt.cancel();
            if (this.logger != null) {
                this.logger.info("客户端超时，已调用stmt.cancel()取消数据库查询");
            }
        } catch (SQLException cancelEx) {
            if (this.logger != null) {
                this.logger.warn("调用stmt.cancel()时发生异常: " + cancelEx.getMessage());
            }
        }
        future.cancel(true);
        throw new SQLTimeoutException("客户端强制终止查询，超时时间: " + this.timeout + "秒");
    }
}

// 调用方式
this.executeWithTimeout(finalStmt, () -> finalStmt.executeUpdate());
```

### 迁移后 (ConnectionHelper)
```java
// 新实现 - 在ConnectionHelper中
public static <T> T executeWithTimeout(PreparedStatement stmt, int timeoutSeconds, SqlExecutor<T> executor) throws SQLException {
    // 如果禁用超时控制，直接执行
    if (!enableTimeoutControl) {
        return executor.execute();
    }

    // 确保线程池已初始化
    ensureTimeoutExecutorInitialized();

    Future<T> future = timeoutExecutor.submit(() -> {
        try {
            return executor.execute();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    });

    try {
        return future.get(timeoutSeconds, TimeUnit.SECONDS);
    } catch (TimeoutException e) {
        try {
            stmt.cancel();
            JDBCErrorLogger.getLogger().info("客户端超时，已调用stmt.cancel()取消数据库查询");
        } catch (SQLException cancelEx) {
            JDBCErrorLogger.getLogger().warn("调用stmt.cancel()时发生异常: " + cancelEx.getMessage());
        }
        future.cancel(true);
        throw new SQLTimeoutException("客户端强制终止查询，超时时间: " + timeoutSeconds + "秒");
    }
}

// 调用方式
ConnectionHelper.executeWithTimeout(finalStmt, this.timeout, () -> finalStmt.executeUpdate());
```

## 主要改进

### 1. 线程安全优化
- **迁移前**: 使用 `Executors.newCachedThreadPool()` 无界线程池
- **迁移后**: 使用有界 `ThreadPoolExecutor`，核心线程2个，最大线程4个，队列容量1000

### 2. 日志记录改进
- **迁移前**: 使用实例的 `this.logger`
- **迁移后**: 使用 `JDBCErrorLogger.getLogger()` 统一错误日志

### 3. 开关控制机制
```java
// 启用/禁用超时控制
ConnectionHelper.setTimeoutControlEnabled(true/false);

// 检查状态
boolean enabled = ConnectionHelper.isTimeoutControlEnabled();
```

### 4. 简化设计
- 静态线程池，启动时初始化
- 守护线程，JVM退出时自动清理
- 简单的开关控制机制

## 使用示例

### 基本使用
```java
EasyQueryImpl query = new EasyQueryImpl("datasourceName");
query.setTimeout(30); // 设置30秒超时

// 自动使用ConnectionHelper.executeWithTimeout
List<EasyRow> results = query.queryForList("SELECT * FROM large_table");
```

### 控制超时机制
```java
// 禁用超时控制（直接执行，不使用线程池）
ConnectionHelper.setTimeoutControlEnabled(false);

// 启用超时控制（默认）
ConnectionHelper.setTimeoutControlEnabled(true);
```

### 手动调用
```java
PreparedStatement stmt = connection.prepareStatement(sql);
String result = ConnectionHelper.executeWithTimeout(stmt, 10, () -> {
    ResultSet rs = stmt.executeQuery();
    // 处理结果集
    return processResultSet(rs);
});
```

## 并发安全性

### 线程池配置
- **核心线程数**: 2
- **最大线程数**: 4  
- **队列容量**: 1000
- **拒绝策略**: CallerRunsPolicy（调用者线程执行）
- **线程类型**: 守护线程

### 同步机制
- 使用 `volatile` 关键字保证可见性
- 使用 `synchronized` 块保证线程池初始化的原子性
- 双重检查锁定模式确保线程安全

## 性能影响

### 优势
- 有界线程池避免OOM风险
- 统一的线程池管理减少资源消耗
- 开关机制支持动态控制

### 注意事项
- 禁用超时控制时性能与原生执行相同
- 启用时有轻微的线程池开销
- 大量并发时队列可能满载，触发拒绝策略
